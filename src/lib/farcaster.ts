import { NeynarAPIClient } from "@neynar/nodejs-sdk";

export interface FarcasterUser {
  fid: number;
  username: string;
  displayName?: string;
  pfpUrl?: string;
  bio?: string;
  followerCount: number;
  followingCount: number;
  verifications: string[];
}

export interface FarcasterCast {
  hash: string;
  text: string;
  author: FarcasterUser;
  timestamp: string;
  replies: number;
  reactions: number;
  recasts: number;
}

class FarcasterClient {
  private client: NeynarAPIClient;

  constructor() {
    const apiKey = process.env.NEYNAR_API_KEY;
    if (!apiKey) {
      throw new Error("NEYNAR_API_KEY is required");
    }
    this.client = new NeynarAPIClient({ apiKey });
  }

  async getUserByFid(fid: number): Promise<FarcasterUser | null> {
    try {
      // const response = await this.client.lookupUserByFid(fid);
      const response = await this.client.fetchBulkUsers({ fids: [fid] });
      if (!response.users?.[0]) {
        return null;
      }

      const user = response.users[0];
      return {
        fid: user.fid,
        username: user.username,
        displayName: user.display_name || user.username,
        pfpUrl: user.pfp_url,
        bio: user.profile?.bio?.text,
        followerCount: user.following_count || 0,
        followingCount: user.following_count || 0,
        verifications: user.verifications || [],
      };
    } catch (error) {
      console.error("Error fetching user by FID:", error);
      return null;
    }
  }

  async getUserByUsername(username: string): Promise<FarcasterUser | null> {
    try {
      const response = await this.client.lookupUserByUsername({ username });
      if (!response.user) {
        return null;
      }

      const user = response.user;
      return {
        fid: user.fid,
        username: user.username,
        displayName: user.display_name || user.username,
        pfpUrl: user.pfp_url,
        bio: user.profile?.bio?.text,
        followerCount: user.following_count || 0,
        followingCount: user.follower_count || 0,
        verifications: user.verifications || [],
      };
    } catch (error) {
      console.error("Error fetching user by username:", error);
      return null;
    }
  }

  async getUserCasts(
    fid: number,
    limit: number = 10
  ): Promise<FarcasterCast[]> {
    try {
      const response = await this.client.fetchCastsForUser(fid, { limit });
      if (!response.result?.casts) {
        return [];
      }

      return response.result.casts.map((cast: any) => ({
        hash: cast.hash,
        text: cast.text,
        author: {
          fid: cast.author.fid,
          username: cast.author.username,
          displayName: cast.author.displayName || cast.author.username,
          pfpUrl: cast.author.pfp?.url,
          bio: cast.author.profile?.bio?.text,
          followerCount: cast.author.followerCount || 0,
          followingCount: cast.author.followingCount || 0,
          verifications: cast.author.verifications || [],
        },
        timestamp: cast.timestamp,
        replies: cast.replies?.count || 0,
        reactions: cast.reactions?.count || 0,
        recasts: cast.recasts?.count || 0,
      }));
    } catch (error) {
      console.error("Error fetching user casts:", error);
      return [];
    }
  }

  async searchUsers(
    query: string,
    limit: number = 10
  ): Promise<FarcasterUser[]> {
    try {
      const response = await this.client.searchUser(query, limit);
      if (!response.result?.users) {
        return [];
      }

      return response.result.users.map((user: any) => ({
        fid: user.fid,
        username: user.username,
        displayName: user.displayName || user.username,
        pfpUrl: user.pfp?.url,
        bio: user.profile?.bio?.text,
        followerCount: user.followerCount || 0,
        followingCount: user.followingCount || 0,
        verifications: user.verifications || [],
      }));
    } catch (error) {
      console.error("Error searching users:", error);
      return [];
    }
  }

  async validateFrameAction(frameActionPayload: any): Promise<boolean> {
    try {
      const response = await this.client.validateFrameAction(
        frameActionPayload
      );
      return response.valid || false;
    } catch (error) {
      console.error("Error validating frame action:", error);
      return false;
    }
  }

  async publishCast(text: string, signerUuid: string): Promise<string | null> {
    try {
      const response = await this.client.publishCast(signerUuid, text);
      return response.hash || null;
    } catch (error) {
      console.error("Error publishing cast:", error);
      return null;
    }
  }

  async followUser(targetFid: number, signerUuid: string): Promise<boolean> {
    try {
      await this.client.followUser(signerUuid, targetFid);
      return true;
    } catch (error) {
      console.error("Error following user:", error);
      return false;
    }
  }

  async unfollowUser(targetFid: number, signerUuid: string): Promise<boolean> {
    try {
      await this.client.unfollowUser(signerUuid, targetFid);
      return true;
    } catch (error) {
      console.error("Error unfollowing user:", error);
      return false;
    }
  }
}

// Singleton instance
let farcasterClient: FarcasterClient | null = null;

export function getFarcasterClient(): FarcasterClient {
  if (!farcasterClient) {
    farcasterClient = new FarcasterClient();
  }
  return farcasterClient;
}

export default FarcasterClient;
