import { NextResponse } from "next/server";
import { getFarcasterMetadata } from "../../../lib/utils";

export async function GET() {
  try {
    return NextResponse.json({
      accountAssociation: {
        header:
          "eyJmaWQiOjExMDY0MzgsInR5cGUiOiJhdXRoIiwia2V5IjoiMHg3Njg3MEI1RmJmQzlkYzc0OUU5MDE1RDk2RTg5QzAyQzQxOURhOEQ2In0",
        payload:
          "eyJkb21haW4iOiI3MWZkLTI0MDAtOTgwMC02MDMyLTUyMi0zMWY4LTkyOWUtMzY0LWRmNi5uZ3Jvay1mcmVlLmFwcCJ9",
        signature:
          "6tgVdGIqlTpTojGpxRG7pMVL16Lh8W2WGLryEumUpbVlv5WoKTrJxpYTdHlViJPUSUosqi7uFA179M34JwUGBxw=",
      },
      frame: {
        name: "Weed Firm App",
        version: "1",
        iconUrl:
          "https://71fd-2400-9800-6032-522-31f8-929e-364-df6.ngrok-free.app/icon.png",
        homeUrl:
          "https://71fd-2400-9800-6032-522-31f8-929e-364-df6.ngrok-free.app",
        imageUrl:
          "https://71fd-2400-9800-6032-522-31f8-929e-364-df6.ngrok-free.app/image.png",
        buttonTitle: "Launch Mini App",
        splashImageUrl:
          "https://71fd-2400-9800-6032-522-31f8-929e-364-df6.ngrok-free.app/splash.png",
        splashBackgroundColor: "#ffffff",
        webhookUrl:
          "https://71fd-2400-9800-6032-522-31f8-929e-364-df6.ngrok-free.app/api/webhook",
        subtitle: "Weed Firm App",
        description:
          "Grow weed, build your empire, and outsmart the heat. Run your virtual hustle and vibe with the Farcaster fam.",
        primaryCategory: "games",
        ogTitle: "Weed FIrm",
        ogDescription:
          "Grow, hustle, and dominate the weed game on Farcaster. Be the boss of your green empire.",
      },
    });
  } catch (error) {
    console.error("Error generating metadata:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
