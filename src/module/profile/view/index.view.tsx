import React from "react";
import { useLang } from "~/hooks/useLang";
import { mockUserProfile } from "~/data/userProfile";
import { useMiniApp } from "@neynar/react";
import {
  mockReferrals,
  mockReferralStats,
  referralRewards,
  referralTips,
} from "~/data/referrals";
import { Check, Clock, Copy, MessageCircle, Share, X } from "lucide-react";
import { cn } from "~/lib/utils";

export default function ProfilePage() {
  const { t, isIndonesian } = useLang();
  const { context } = useMiniApp();
  const user = context?.user;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString(isIndonesian ? "id-ID" : "en-US", {
      year: "numeric",
      month: "short",
    });
  };

  const statusColor = {
    valid: "text-green-600",
    pending: "text-yellow-600",
    inactive: "text-red-600",
    just_joined: "text-blue-600",
  };

  const statusBgColor = {
    valid: "bg-green-400",
    pending: "bg-yellow-400",
    inactive: "bg-red-400",
    just_joined: "bg-blue-400",
  };

  const statusIcon = {
    valid: <Check className="size-6" strokeWidth={3} />,
    pending: <Clock className="size-6" strokeWidth={3} />,
    inactive: <X className="size-6" strokeWidth={3} />,
    just_joined: "🆕",
  };

  const getStatusText = (status: string, harvestCount: number) => {
    switch (status) {
      case "valid":
        return t("referral.validHarvests", { count: harvestCount });
      case "pending":
        return t("referral.pendingHarvests", { count: harvestCount });
      case "inactive":
        return t("referral.inactive", { count: harvestCount });
      case "just_joined":
        return t("referral.justJoined");
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6 p-4">
      {/* Profile Header */}
      <div className="bg-background rounded-xl p-6 text-center">
        <div className="mx-auto mb-4 h-20 w-20 overflow-hidden rounded-full bg-linear-to-br from-green-400 to-green-600">
          {user ? (
            <img
              src={user?.pfpUrl ?? mockUserProfile.profileImage}
              alt={user?.username ?? mockUserProfile.username}
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center text-2xl font-bold text-white">
              {mockUserProfile.username.charAt(0).toUpperCase()}
            </div>
          )}
        </div>

        <h1 className="mb-1 text-xl font-bold text-gray-800">
          @{user?.username ?? mockUserProfile.username}
        </h1>
        <p className="mb-4 text-gray-600">
          {t("header.level", { level: mockUserProfile.level })}
        </p>

        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {mockUserProfile.weedBalance}
            </div>
            <div className="text-sm text-gray-600">
              {t("profile.weedEarned")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              #{mockUserProfile.globalRank}
            </div>
            <div className="text-sm text-gray-600">
              {t("profile.globalRank")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {mockUserProfile.plantsGrown}
            </div>
            <div className="text-sm text-gray-600">
              {t("profile.plantsGrown")}
            </div>
          </div>
        </div>
      </div>

      {/* Farm Statistics */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-4 text-lg font-semibold text-gray-800">
          {t("profile.farmStats")}
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">{t("profile.totalHarvests")}</span>
            <span className="font-semibold text-gray-800">
              {mockUserProfile.totalHarvests}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-600">{t("profile.daysActive")}</span>
            <span className="font-semibold text-gray-800">
              {mockUserProfile.daysActive}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-600">{t("profile.bestStreak")}</span>
            <span className="font-semibold text-gray-800">
              {t("home.days", { count: mockUserProfile.bestStreak })}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-600">{t("profile.joined")}</span>
            <span className="font-semibold text-gray-800">
              {formatDate(mockUserProfile.joinedAt)}
            </span>
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-4 text-lg font-semibold text-gray-800">
          {t("profile.achievements")}
        </h3>

        <div className="grid grid-cols-3 gap-4">
          {mockUserProfile.achievements.map((achievement) => (
            <div
              key={achievement.id}
              className={`rounded-xl border-2 p-4 text-center ${
                achievement.unlocked
                  ? "border-green-200 bg-green-50"
                  : "border-gray-200 bg-gray-50"
              }`}
            >
              <div
                className={`mb-2 text-3xl ${
                  achievement.unlocked ? "" : "opacity-50 grayscale"
                }`}
              >
                {achievement.icon}
              </div>
              <div
                className={`text-sm font-medium ${
                  achievement.unlocked ? "text-green-800" : "text-gray-500"
                }`}
              >
                {achievement.name}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Header Card */}
      <div className="rounded-xl bg-linear-to-r from-purple-500 to-pink-500 p-4 text-white">
        <h1 className="mb-2 text-lg font-bold">
          💝 {t("referral.inviteFriends")}
        </h1>
        <p className="mb-4 text-sm opacity-90">{t("referral.earnTokens")}</p>

        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold">
              {mockReferralStats.totalInvited}
            </div>
            <div className="text-xs opacity-80">
              {t("referral.totalInvited")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {mockReferralStats.validReferrals}
            </div>
            <div className="text-xs opacity-80">
              {t("referral.validReferrals")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {mockReferralStats.weedEarned}
            </div>
            <div className="text-xs opacity-80">{t("referral.weedEarned")}</div>
          </div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-3 text-lg font-semibold text-gray-800">
          {t("referral.yourLink")}
        </h3>

        <div className="mb-4 flex items-center space-x-2">
          <div className="flex-1 rounded-lg bg-gray-100 p-3 text-sm text-gray-700">
            https://weedfarm.app/ref/weedfarmer
          </div>
          <button className="rounded-lg bg-green-500 p-3 text-white transition-colors hover:bg-green-600">
            <Copy className="h-4 w-4" />
          </button>
        </div>

        <div className="grid gap-3">
          <button className="flex items-center justify-center space-x-2 rounded-lg bg-blue-500 py-3 text-white transition-colors hover:bg-blue-600">
            <Share className="h-4 w-4" />
            <span className="text-sm font-medium">
              {t("referral.shareOnTelegram")}
            </span>
          </button>
          <button className="flex items-center justify-center space-x-2 rounded-lg bg-green-500 py-3 text-white transition-colors hover:bg-green-600">
            <MessageCircle className="h-4 w-4" />
            <span className="text-sm font-medium">
              {t("referral.shareOnWhatsApp")}
            </span>
          </button>
        </div>
      </div>

      {/* Referral Rewards */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-4 text-lg font-semibold text-gray-800">
          {t("referral.rewards")}
        </h3>

        <div className="space-y-3">
          {referralRewards.map((reward) => (
            <div
              key={reward.id}
              className="flex items-center space-x-3 rounded-lg bg-gray-50 p-3"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                <span className="text-lg">{reward.icon}</span>
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-800">{reward.title}</p>
                <p className="text-sm text-gray-600">{reward.description}</p>
              </div>
              <div className="text-right">
                <p className="font-semibold text-green-600">
                  +{reward.weedAmount} WEED
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Your Referrals */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-4 text-lg font-semibold text-gray-800">
          {t("referral.yourReferrals")}
        </h3>

        <div className="space-y-3">
          {mockReferrals.map((referral) => (
            <div
              key={referral.id}
              className={cn(
                "flex items-center space-x-3 py-2",
                statusColor[referral.status],
              )}
            >
              <div
                className={cn(
                  "h-14 w-14 overflow-hidden rounded-full text-white flex items-center justify-center",
                  statusBgColor[referral.status],
                )}
              >
                {statusIcon[referral.status]}
              </div>

              <div className="flex-1">
                <p className="font-medium text-gray-800">
                  @{referral.username}
                </p>
                <div className="flex items-center gap-2">
                  <span
                    className={cn(
                      "text-sm leading-none",
                      statusColor[referral.status],
                    )}
                  >
                    {getStatusText(referral.status, referral.harvestCount)}
                  </span>
                </div>
                <p className="text-xs text-gray-500">{referral.timeAgo}</p>
              </div>

              <div className="text-right">
                <p className="font-semibold text-green-600">
                  +{referral.weedEarned} WEED
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Referral Tips */}
      <div className="rounded-xl border border-yellow-200 bg-yellow-50 p-4">
        <h3 className="mb-3 text-lg font-semibold text-yellow-800">
          💡 {t("referral.tips")}
        </h3>

        <div className="space-y-2">
          {referralTips.map((tip, index) => (
            <div key={index} className="flex items-start space-x-2">
              <div className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full bg-green-500">
                <span className="text-xs text-white">✓</span>
              </div>
              <p className="text-sm text-yellow-800">{tip}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
