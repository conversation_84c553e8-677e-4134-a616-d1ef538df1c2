/* eslint-disable @next/next/no-img-element */
import React, { useState } from 'react'
import { useLang } from '~/hooks/useLang'
import { TrendingUp, TrendingDown } from 'lucide-react'
import { mockLeaderboardData, currentUserRank } from '~/data/leaderboard'

export default function LeaderboardPage() {
  const { t } = useLang()
  const [activeTab, setActiveTab] = useState<'weekly' | 'monthly' | 'all'>('weekly')

  const tabs = [
    { id: 'weekly', label: t('leaderboard.weekly') },
    { id: 'monthly', label: t('leaderboard.monthly') },
    { id: 'all', label: t('leaderboard.allTime') },
  ] as const

  const getPodiumPosition = (rank: number) => {
    if (rank === 1) return { bg: 'bg-yellow-400', text: 'text-yellow-800', size: 'h-20' }
    if (rank === 2) return { bg: 'bg-gray-300', text: 'text-gray-700', size: 'h-16' }
    if (rank === 3) return { bg: 'bg-orange-400', text: 'text-orange-800', size: 'h-12' }
    return { bg: 'bg-gray-200', text: 'text-gray-600', size: 'h-8' }
  }

  const getChangeIcon = (change?: number) => {
    if (!change || change === 0) return null
    if (change > 0) return <TrendingUp className="w-3 h-3 text-green-500" />
    return <TrendingDown className="w-3 h-3 text-red-500" />
  }

  const topThree = mockLeaderboardData.slice(0, 3)
  const restOfLeaderboard = mockLeaderboardData.slice(3)

  return (
    <div className="p-4 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-xl font-bold text-gray-800 mb-2">
          🏆 {t('leaderboard.title')}
        </h1>
        <p className="text-sm text-gray-600">
          {t('leaderboard.topFarmers')}
        </p>
      </div>

      {/* Period Tabs */}
      <div className="flex bg-gray-100 rounded-xl p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-gray-800 shadow-xs'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Podium */}
      <div className="bg-white rounded-xl p-6">
        <div className="flex items-end justify-center space-x-4 mb-6">
          {/* 2nd Place */}
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full mb-2 overflow-hidden">
              <img 
                src={topThree[1]?.profileImage || '/avatars/default.png'} 
                alt={topThree[1]?.username}
                className="w-full h-full object-cover"
              />
            </div>
            <div className={`w-16 ${getPodiumPosition(2).size} ${getPodiumPosition(2).bg} rounded-t-lg flex items-center justify-center`}>
              <span className={`text-2xl font-bold ${getPodiumPosition(2).text}`}>2</span>
            </div>
            <div className="text-center mt-2">
              <p className="text-sm font-medium text-gray-800">@{topThree[1]?.username}</p>
              <p className="text-xs text-gray-600">{topThree[1]?.weedBalance} WEED</p>
            </div>
          </div>

          {/* 1st Place */}
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 rounded-full mb-2 overflow-hidden border-4 border-yellow-400">
              <img 
                src={topThree[0]?.profileImage || '/avatars/default.png'} 
                alt={topThree[0]?.username}
                className="w-full h-full object-cover"
              />
            </div>
            <div className={`w-20 ${getPodiumPosition(1).size} ${getPodiumPosition(1).bg} rounded-t-lg flex items-center justify-center`}>
              <span className={`text-3xl font-bold ${getPodiumPosition(1).text}`}>1</span>
            </div>
            <div className="text-center mt-2">
              <p className="text-sm font-medium text-gray-800">@{topThree[0]?.username}</p>
              <p className="text-xs text-gray-600">{topThree[0]?.weedBalance} WEED</p>
            </div>
          </div>

          {/* 3rd Place */}
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full mb-2 overflow-hidden">
              <img 
                src={topThree[2]?.profileImage || '/avatars/default.png'} 
                alt={topThree[2]?.username}
                className="w-full h-full object-cover"
              />
            </div>
            <div className={`w-16 ${getPodiumPosition(3).size} ${getPodiumPosition(3).bg} rounded-t-lg flex items-center justify-center`}>
              <span className={`text-2xl font-bold ${getPodiumPosition(3).text}`}>3</span>
            </div>
            <div className="text-center mt-2">
              <p className="text-sm font-medium text-gray-800">@{topThree[2]?.username}</p>
              <p className="text-xs text-gray-600">{topThree[2]?.weedBalance} WEED</p>
            </div>
          </div>
        </div>
      </div>

      {/* Current User Rank */}
      <div className="bg-green-50 border-2 border-green-200 rounded-xl p-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-full overflow-hidden">
            <img 
              src={currentUserRank.profileImage || '/avatars/default.png'} 
              alt={currentUserRank.username}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1">
            <p className="font-semibold text-gray-800">
              @{currentUserRank.username} {t('leaderboard.you')}
            </p>
            <p className="text-sm text-gray-600">
              {t('leaderboard.earned', { amount: currentUserRank.weedBalance })}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-green-600">
              #{currentUserRank.rank}
            </div>
            <div className="text-xs text-green-600">
              {t('home.topPercent', { percent: currentUserRank.topPercent })}
            </div>
          </div>
        </div>
      </div>

      {/* All Rankings */}
      <div className="bg-white rounded-xl p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          {t('leaderboard.allRankings')}
        </h3>
        
        <div className="space-y-3">
          {restOfLeaderboard.map((user) => (
            <div key={user.id} className="flex items-center space-x-3 py-2">
              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                <span className="text-sm font-bold text-gray-600">{user.rank}</span>
              </div>
              
              <div className="w-10 h-10 rounded-full overflow-hidden">
                <img 
                  src={user.profileImage || '/avatars/default.png'} 
                  alt={user.username}
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="flex-1">
                <p className="font-medium text-gray-800">@{user.username}</p>
                <p className="text-sm text-gray-600">Level {user.level}</p>
              </div>
              
              <div className="text-right">
                <p className="font-semibold text-gray-800">{user.weedBalance} WEED</p>
                <div className="flex items-center justify-end space-x-1 text-xs text-gray-500">
                  {getChangeIcon(user.change)}
                  <span>
                    {user.change === 0 ? t('leaderboard.same') : 
                     user.change && user.change > 0 ? 
                     `↑ ${user.change} ${user.change === 1 ? t('leaderboard.position', { count: user.change }) : t('leaderboard.positions', { count: user.change })}` :
                     `↓ ${Math.abs(user.change || 0)} ${Math.abs(user.change || 0) === 1 ? t('leaderboard.position', { count: Math.abs(user.change || 0) }) : t('leaderboard.positions', { count: Math.abs(user.change || 0) })}`
                    }
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <button className="w-full mt-4 py-3 text-green-600 font-medium text-sm hover:bg-green-50 rounded-lg transition-colors">
          {t('leaderboard.viewMore')}
        </button>
      </div>
    </div>
  )
}

