import { useAccount, useConnect } from "wagmi";
import React, { useState } from "react";
import { Wallet, Zap } from "lucide-react";

import { useLang } from "~/hooks/useLang";
import { storeItems, nftCollection } from "~/data/storeItems";
import { mockUserProfile } from "~/data/userProfile";
import { cn } from "~/lib/utils";

export default function StorePage() {
  const { t } = useLang();
  const [activeTab, setActiveTab] = useState<
    "hashrate_boost" | "nft_weed" | "automation"
  >("hashrate_boost");

  const tabs = [
    { id: "hashrate_boost", label: t("store.hashrateBoost") },
    { id: "nft_weed", label: t("store.nftWeed") },
    { id: "automation", label: t("store.automation") },
  ] as const;

  const getItemsByCategory = (category: string) => {
    return storeItems.filter((item) => item.category === category);
  };

  const getButtonColor = (color: string) => {
    const colors = {
      blue: "bg-blue-500 hover:bg-blue-600",
      cyan: "bg-cyan-500 hover:bg-cyan-600",
      purple: "bg-purple-500 hover:bg-purple-600",
      orange: "bg-orange-500 hover:bg-orange-600",
      green: "bg-green-500 hover:bg-green-600",
    };
    return (
      colors[color as keyof typeof colors] || "bg-gray-500 hover:bg-gray-600"
    );
  };

  const { isConnected, address } = useAccount();
  const { connect, connectors } = useConnect();

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <div className="text-center">
        <h1 className="mb-2 text-xl font-bold text-gray-800">
          🛒 {t("store.title")}
        </h1>
        <div className="flex items-center justify-center space-x-4 text-sm">
          <div className="flex items-center space-x-1">
            <img
              src="/icons/cannabis-leaf.png"
              alt="WEED"
              className="h-4 w-4"
            />
            <span className="font-semibold text-green-600">
              {mockUserProfile.weedBalance} WEED
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-blue-600">💎</span>
            <span className="font-semibold text-blue-600">
              {mockUserProfile.ethBalance} ETH
            </span>
          </div>
        </div>
      </div>

      {isConnected ? (
        <div className="flex flex-col gap-1 rounded-xl bg-linear-to-r from-purple-500 to-pink-500 p-1 px-3 py-2 text-white">
          <div className="flex flex-1 items-center rounded-lg text-xs font-medium transition-colors">
            <Zap className="fil mr-2 size-3 text-yellow-400" />
            {t("store.connectWallet")}
          </div>

          <div className="truncate font-mono text-xs">{address}</div>
        </div>
      ) : (
        <div className="rounded-xl border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-start space-x-3">
            <div className="text-blue-600">💎</div>
            <div>
              <h3 className="mb-1 font-semibold text-blue-800">
                {t("store.ethereumPayments")}
              </h3>
              <p className="mb-3 text-sm text-blue-700">
                {t("store.premiumItems")}
              </p>
              <button
                onClick={() => {
                  connect({ connector: connectors[0] });
                }}
                className="flex items-center space-x-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
              >
                <Wallet className="h-4 w-4" />
                <span>{t("store.connectWallet")}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Category Tabs */}
      <div className="flex rounded-xl bg-gray-100 p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={cn(
              "flex-1 rounded-lg px-3 py-2 text-xs font-medium transition-colors",
              activeTab === tab.id
                ? "bg-white text-gray-800 shadow-xs"
                : "text-gray-600 hover:text-gray-800",
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Store Items */}
      <div className="space-y-4">
        {getItemsByCategory(activeTab).map((item) => (
          <div
            key={item.id}
            className="rounded-xl border border-gray-100 bg-white p-4"
          >
            <div className="flex items-start space-x-3">
              <div
                className={`flex h-12 w-12 items-center justify-center rounded-xl text-2xl ${
                  item.color === "blue"
                    ? "bg-blue-100"
                    : item.color === "cyan"
                      ? "bg-cyan-100"
                      : item.color === "purple"
                        ? "bg-purple-100"
                        : item.color === "orange"
                          ? "bg-orange-100"
                          : "bg-green-100"
                }`}
              >
                {item.icon}
              </div>

              <div className="flex-1">
                <h3 className="mb-1 font-semibold text-gray-800">
                  {item.name}
                </h3>
                <p className="mb-2 text-sm text-gray-600">{item.description}</p>

                {item.boost && (
                  <div className="mb-2 text-xs text-gray-500">
                    {item.boost}%
                  </div>
                )}

                {item.duration && (
                  <div className="mb-2 text-xs text-gray-500">
                    ⏱️{" "}
                    {t("store.duration", {
                      days: Math.floor(item.duration / 24),
                    })}
                  </div>
                )}

                {item.permanent && (
                  <div className="mb-2 text-xs text-green-600">
                    ♾️ {t("store.permanentUpgrade")}
                  </div>
                )}
              </div>

              <div className="text-right">
                <div className="mb-2 font-bold text-gray-800">
                  {item.priceEth
                    ? `${item.priceEth} ETH`
                    : `${item.priceWeed} WEED`}
                </div>
                <button
                  className={`rounded-lg px-4 py-2 text-sm font-medium text-white transition-colors ${getButtonColor(
                    item.color,
                  )}`}
                >
                  {t("store.buy")}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* NFT Collection (when NFT tab is active) */}
      {activeTab === "nft_weed" && (
        <div className="rounded-xl bg-linear-to-r from-purple-500 to-pink-500 p-4 text-white">
          <h3 className="mb-2 font-bold">✨ {t("store.legendaryNFT")}</h3>

          <div className="mb-4 grid grid-cols-3 gap-3">
            {nftCollection.map((nft) => (
              <div
                key={nft.id}
                className="rounded-lg bg-white/20 p-3 text-center"
              >
                <div
                  className={`mx-auto mb-2 h-8 w-8 rounded-full ${
                    nft.color === "yellow"
                      ? "bg-yellow-400"
                      : nft.color === "white"
                        ? "bg-white"
                        : "bg-red-400"
                  }`}
                ></div>
                <div className="mb-1 text-xs font-medium">{nft.name}</div>
                <div className="text-xs opacity-80">
                  {t("store.yieldBoost", { percent: nft.yield })}
                </div>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between">
            <div>
              <div className="font-semibold">{t("store.randomNFT")}</div>
              <div className="text-sm opacity-80">
                {t("store.permanentHashrate")}
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">0.01 ETH</div>
              <button className="rounded-lg bg-white px-4 py-2 text-sm font-medium text-purple-600">
                {t("store.mint")}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Special Items */}
      <div className="space-y-4">
        {/* Mega Hashrate */}
        <div className="rounded-xl bg-linear-to-r from-orange-400 to-red-500 p-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🚀</div>
              <div>
                <h3 className="font-bold">{t("store.megaHashrate")}</h3>
                <p className="text-sm opacity-90">
                  {t("store.fasterGeneration")}
                </p>
                <div className="mt-1 inline-block rounded bg-white/20 px-2 py-1 text-xs">
                  ⚡ {t("store.limitedTime")}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">0.003 ETH</div>
              <button className="rounded-lg bg-white px-4 py-2 text-sm font-medium text-orange-600">
                {t("store.buy")}
              </button>
            </div>
          </div>
        </div>

        {/* Farm Expansion */}
        <div className="rounded-xl bg-linear-to-r from-green-400 to-green-600 p-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🌱</div>
              <div>
                <h3 className="font-bold">{t("store.farmExpansion")}</h3>
                <p className="text-sm opacity-90">{t("store.addPlantSlot")}</p>
                <div className="text-xs opacity-80">
                  + {t("store.growMultiple")}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="font-bold">0.008 ETH</div>
              <button className="rounded-lg bg-white px-4 py-2 text-sm font-medium text-green-600">
                {t("store.buy")}
              </button>
            </div>
          </div>
        </div>

        {/* Genetics Lab */}
        <div className="rounded-xl bg-linear-to-r from-blue-500 to-purple-600 p-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🧬</div>
              <div>
                <h3 className="font-bold">{t("store.geneticsLab")}</h3>
                <p className="text-sm opacity-90">{t("store.customStrains")}</p>
                <div className="text-xs opacity-80">
                  🧪 {t("store.breedGenetics")}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="font-bold">0.02 ETH</div>
              <button className="rounded-lg bg-white px-4 py-2 text-sm font-medium text-blue-600">
                {t("store.unlock")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
