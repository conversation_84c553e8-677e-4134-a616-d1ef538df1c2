import React from "react";
import { useLang } from "~/hooks/useLang";
import { Droplets } from "lucide-react";
import { mockUserProfile, mockPlantData } from "~/data/userProfile";
import { mockActivities, mockUserStats } from "~/data/activities";
import { Progress } from "./ui/progress";

export default function HomePage() {
  const { t } = useLang();

  const getStageIcon = (stage: string, isActive: boolean) => {
    const baseClasses =
      "w-8 h-8 rounded-full flex items-center justify-center leading-none font-medium";
    const activeClasses = "bg-green-500/50 text-white";
    const inactiveClasses = "bg-gray-200 text-gray-400";

    return (
      <div
        className={`${baseClasses} ${isActive ? activeClasses : inactiveClasses}`}
      >
        {stage === "seed" && "🌰"}
        {stage === "sprout" && "🌱"}
        {stage === "growing" && "🌿"}
        {stage === "mature" && "🍃"}
        {stage === "harvest" && "🌾"}
      </div>
    );
  };

  const stages = ["seed", "sprout", "growing", "mature", "harvest"];
  const currentStageIndex = stages.indexOf(mockPlantData.stage);

  return (
    <div className="space-y-6 p-4">
      {/* Farm Title */}
      <div className="text-center">
        <h1 className="mb-1 text-xl font-bold text-gray-800">
          {t("home.title")}
        </h1>
      </div>

      {/* Main Plant Card */}
      <div className="rounded-2xl bg-white p-6 shadow-xs">
        <div className="mb-4 text-center">
          <div className="mx-auto mb-4 h-48 w-48 overflow-hidden rounded-xl bg-linear-to-b from-green-100 to-green-200">
            <img
              src="/images/main-plant.png"
              alt="Cannabis Plant"
              className="h-full w-full object-cover"
            />
          </div>

          <h2 className="mb-1 text-lg font-semibold text-gray-800">
            {t("home.plantStage")}
          </h2>
          <p className="text-sm text-gray-600">
            {t("home.plantProgress", { progress: mockPlantData.progress })}
          </p>
        </div>

        {/* Growth Stages */}
        <div className="mb-6 flex items-center justify-between">
          {stages.map((stage, index) => (
            <div key={stage} className="flex flex-col items-center space-y-2">
              {getStageIcon(stage, index <= currentStageIndex)}
              <span className="text-xs text-gray-500 capitalize">
                {t(`home.stage.${stage}`)}
              </span>
            </div>
          ))}
        </div>

        {/* TODO: value watering */}
        <Progress
          value={mockPlantData.progress}
          className="mb-3 h-2 rounded-xl [--primary:var(--color-green-500)]"
        />

        {/* Water Button */}
        <button
          className="w-full items-center justify-center space-x-2 rounded-xl bg-green-500 px-6 py-3 transition-colors hover:bg-green-600"
          disabled={!mockPlantData.canWater}
        >
          <div className="flex items-center justify-center gap-2 font-semibold text-white">
            <Droplets className="h-5 w-5" />
            <span>{t("home.waterPlant")}</span>
          </div>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        <div className="rounded-xl bg-white p-4">
          <div className="mb-2 flex items-center space-x-2">
            <span className="text-green-600">🌱</span>
            <span className="text-sm font-medium text-gray-700">
              {t("home.plantsGrown")}
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-800">
            {mockUserStats.plantsGrown}
          </div>
          <div className="text-xs text-gray-500">
            {t("home.thisWeek", { count: mockUserStats.plantsGrownThisWeek })}
          </div>
        </div>

        <div className="rounded-xl bg-white p-4">
          <div className="mb-2 flex items-center space-x-2">
            <span className="text-yellow-600">💰</span>
            <span className="text-sm font-medium text-gray-700">
              {t("home.weedEarned")}
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-800">
            {mockUserStats.weedEarned}
          </div>
          <div className="text-xs text-gray-500">
            {t("home.thisWeek", { count: mockUserStats.weedEarnedThisWeek })}
          </div>
        </div>

        <div className="rounded-xl bg-white p-4">
          <div className="mb-2 flex items-center space-x-2">
            <span className="text-orange-600">🏆</span>
            <span className="text-sm font-medium text-gray-700">
              {t("home.rank")}
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-800">
            #{mockUserStats.rank}
          </div>
          <div className="text-xs text-gray-500">
            {t("home.topPercent", { percent: mockUserStats.topPercent })}
          </div>
        </div>

        <div className="rounded-xl bg-white p-4">
          <div className="mb-2 flex items-center space-x-2">
            <span className="text-blue-600">📅</span>
            <span className="text-sm font-medium text-gray-700">
              {t("home.dailyStreak")}
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-800">
            {t("home.days", { count: mockUserStats.dailyStreak })}
          </div>
          <div className="text-xs text-gray-500">{t("home.keepItUp")}</div>
        </div>
      </div>

      {/* Next Level Reward */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-3 text-lg font-semibold text-gray-800">
          {t("home.nextLevelReward")}
        </h3>

        <div className="mb-3 flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
            <span className="text-green-600">🎁</span>
          </div>
          <div className="flex-1">
            <p className="font-medium text-gray-800">
              {t("home.levelReward", { level: mockUserProfile.level + 1 })}
            </p>
            <p className="text-sm text-gray-600">+2.5 WEED Tokens</p>
          </div>
        </div>

        <div className="mb-2">
          <div className="mb-1 flex justify-between text-sm text-gray-600">
            <span>
              {t("home.progressToLevel", { level: mockUserProfile.level + 1 })}
            </span>
          </div>
          <div className="h-2 w-full rounded-full bg-gray-200">
            <Progress
              value={75}
              className="h-2 rounded-full [--primary:var(--color-green-500)]"
            />
          </div>
        </div>

        <p className="text-xs text-gray-500">{t("home.rewardsAvailable")}</p>
      </div>

      {/* Recent Activity */}
      <div className="rounded-xl bg-white p-4">
        <h3 className="mb-4 text-lg font-semibold text-gray-800">
          {t("home.recentActivity")}
        </h3>

        <div className="space-y-3">
          {mockActivities.slice(0, 3).map((activity) => (
            <div key={activity.id} className="flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-100">
                <span className="text-sm">{activity.icon}</span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800">
                  {activity.description}
                </p>
                <p className="text-xs text-gray-500">
                  {activity.createdAt.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <div className="text-sm font-semibold text-green-600">
                +{activity.weedEarned} WEED
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
