import { create } from "zustand";

export type RouteState = {
  currentPage:
    | "home"
    | "store"
    | "leaderboard"
    | "rewards"
    | "referral"
    | "profile";
};

export type RouteAction = {
    setCurrentPage: (page: RouteStore["currentPage"]) => void;
}

export type RouteStore = RouteState & RouteAction;

export const useRouteStore = create<RouteStore>()((set) => ({
    currentPage: "home",
    setCurrentPage: (page) => set({ currentPage: page }),
}))