import React from "react";
import { useLang } from "~/hooks/useLang";
import { Home, Store, Trophy, Gift, User, Settings } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

interface LayoutProps {
  children: React.ReactNode;
  currentPage:
    | "home"
    | "store"
    | "leaderboard"
    | "rewards"
    | "referral"
    | "profile";
  onPageChange: (
    page: "home" | "store" | "leaderboard" | "rewards" | "referral" | "profile"
  ) => void;
  userProfile: {
    username: string;
    level: number;
    weedBalance: number;
    profileImage?: string;
  };
}

export default function Layout({
  children,
  currentPage,
  onPageChange,
  userProfile,
}: LayoutProps) {
  const { t } = useLang();

  const navItems = [
    { id: "home", icon: Home, label: t("nav.home") },
    { id: "store", icon: Store, label: t("nav.store") },
    { id: "leaderboard", icon: Trophy, label: t("nav.leaderboard") },
    { id: "rewards", icon: Gift, label: t("nav.rewards") },
    { id: "profile", icon: User, label: t("nav.profile") },
  ] as const;

  return (
    <div className="min-h-screen bg-muted flex flex-col max-w-lg mx-auto">
      {/* Header */}
      <header className="bg-white shadow-xs p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-linear-to-br from-green-400 to-green-600 flex items-center justify-center">
            {userProfile.profileImage ? (
              <Avatar>
                <AvatarImage
                  src={userProfile.profileImage}
                  className="object-cover"
                />
                <AvatarFallback>
                  {userProfile.username.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            ) : (
              <span className="text-white font-bold text-sm">
                {userProfile.username.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          <div>
            <p className="text-sm text-gray-600">@{userProfile.username}</p>
            <p className="text-xs text-gray-500">
              {t("header.level", { level: userProfile.level })}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1 bg-green-100 px-3 py-1 rounded-full">
            <img
              src="/icons/cannabis-leaf.png"
              alt="WEED"
              className="w-4 h-4"
            />
            <span className="text-green-700 font-semibold text-sm">
              {userProfile.weedBalance.toFixed(1)} {t("header.weed")}
            </span>
          </div>
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 pb-16">{children}</main>

      {/* Bottom Navigation */}
      <nav className="bg-white fixed bottom-0 h-16 left-0 w-full flex items-center justify-center border-t border-gray-200 px-2 py-1">
        <div className="flex justify-between w-full">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentPage === item.id;

            return (
              <button
                key={item.id}
                onClick={() => onPageChange(item.id)}
                className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                  isActive
                    ? "text-green-600 bg-green-50"
                    : "text-gray-400 hover:text-gray-600"
                }`}
              >
                <Icon className="w-5 h-5 mb-1" />
                <span className="text-xs font-medium">{item.label}</span>
              </button>
            );
          })}
        </div>
      </nav>
    </div>
  );
}
