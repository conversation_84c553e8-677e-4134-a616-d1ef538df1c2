"use client";

import React, { useEffect } from "react";
import Layout from "~/components/layout";
import HomePage from "~/module/home/<USER>/index.view";
import LeaderboardPage from "~/module/leaderboard/view/index.view";
import StorePage from "~/module/store/view/index.view";
import RewardsPage from "~/module/rewards/view/index.view";
import ProfilePage from "~/module/profile/view/index.view";
import { mockUserProfile } from "~/data/userProfile";
import { useMiniApp } from "@neynar/react";
import { useRouteStore } from "~/store/route.store";
import { useQuery } from "@tanstack/react-query";

export default function App() {
  const { currentPage, setCurrentPage } = useRouteStore();
  const { context } = useMiniApp();
  const user = context?.user;
  
  const {} = useQuery({
    queryKey: ['actions-ready'],
    queryFn
  })

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [currentPage]);

  const renderPage = () => {
    switch (currentPage) {
      case "home":
        return <HomePage />;
      case "store":
        return <StorePage />;
      case "leaderboard":
        return <LeaderboardPage />;
      case "rewards":
        return <RewardsPage />;
      case "profile":
        return <ProfilePage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <Layout
      currentPage={currentPage}
      onPageChange={setCurrentPage}
      userProfile={{
        username: user?.username ?? mockUserProfile.username,
        level: mockUserProfile.level,
        weedBalance: mockUserProfile.weedBalance,
        profileImage: user?.pfpUrl ?? mockUserProfile.profileImage,
      }}
    >
      {renderPage()}
    </Layout>
  );
}
