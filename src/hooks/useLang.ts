import { useState, useEffect } from 'react'

type Language = 'en' | 'id'

interface Translations {
  [key: string]: {
    en: string
    id: string
  }
}

const translations: Translations = {
  // Header
  'header.level': {
    en: 'Level {level} Farmer',
    id: 'Petani Level {level}'
  },
  'header.weed': {
    en: 'WEED',
    id: 'WEED'
  },

  // Home Page
  'home.title': {
    en: 'Your Weed Farm',
    id: 'Kebun Ganja Anda'
  },
  'home.nextGrowth': {
    en: '{time} to next growth',
    id: '{time} untuk pertumbuhan berikutnya'
  },
  'home.stage.seed': {
    en: 'Seed',
    id: 'Benih'
  },
  'home.stage.sprout': {
    en: 'Sprout',
    id: 'Kecambah'
  },
  'home.stage.growing': {
    en: 'Growing',
    id: 'Tumbuh'
  },
  'home.stage.mature': {
    en: 'Mature',
    id: 'Dewasa'
  },
  'home.stage.harvest': {
    en: 'Harvest',
    id: 'Panen'
  },
  'home.plantStage': {
    en: 'Young Flowering Stage',
    id: 'Tahap Berbunga Muda'
  },
  'home.plantProgress': {
    en: 'Your plant is {progress}% to harvest',
    id: 'Tanaman Anda {progress}% menuju panen'
  },
  'home.waterPlant': {
    en: 'Water Plant',
    id: 'Siram Tanaman'
  },
  'home.plantsGrown': {
    en: 'Plants Grown',
    id: 'Tanaman Ditanam'
  },
  'home.weedEarned': {
    en: 'WEED Earned',
    id: 'WEED Diperoleh'
  },
  'home.rank': {
    en: 'Rank',
    id: 'Peringkat'
  },
  'home.topPercent': {
    en: 'Top {percent}%',
    id: 'Top {percent}%'
  },
  'home.dailyStreak': {
    en: 'Daily Streak',
    id: 'Streak Harian'
  },
  'home.keepItUp': {
    en: 'Keep it up!',
    id: 'Pertahankan!'
  },
  'home.nextLevelReward': {
    en: 'Next Level Reward',
    id: 'Hadiah Level Berikutnya'
  },
  'home.levelReward': {
    en: 'Level {level} Reward',
    id: 'Hadiah Level {level}'
  },
  'home.progressToLevel': {
    en: 'Progress to Level {level}',
    id: 'Progres ke Level {level}'
  },
  'home.rewardsAvailable': {
    en: 'Rewards will be available for withdrawal after reaching Level 10.',
    id: 'Hadiah akan tersedia untuk penarikan setelah mencapai Level 10.'
  },
  'home.recentActivity': {
    en: 'Recent Activity',
    id: 'Aktivitas Terbaru'
  },
  'home.activity.watered': {
    en: 'Plant Watered',
    id: 'Tanaman Disiram'
  },
  'home.activity.harvested': {
    en: 'Plant Harvested',
    id: 'Tanaman Dipanen'
  },
  'home.activity.levelUp': {
    en: 'Level Up to Level {level}',
    id: 'Naik ke Level {level}'
  },
  'home.thisWeek': {
    en: '+{count} this week',
    id: '+{count} minggu ini'
  },
  'home.days': {
    en: '{count} days',
    id: '{count} hari'
  },

  // Navigation
  'nav.home': {
    en: 'Home',
    id: 'Beranda'
  },
  'nav.store': {
    en: 'Store',
    id: 'Toko'
  },
  'nav.leaderboard': {
    en: 'Leaderboard',
    id: 'Papan Peringkat'
  },
  'nav.rewards': {
    en: 'Rewards',
    id: 'Hadiah'
  },
  'nav.referral': {
    en: 'Referral',
    id: 'Rujukan'
  },
  'nav.profile': {
    en: 'Profile',
    id: 'Profil'
  },

  // Store Page
  'store.title': {
    en: 'Farm Store',
    id: 'Toko Pertanian'
  },
  'store.hashrateBoost': {
    en: 'Hashrate Boost',
    id: 'Boost Hashrate'
  },
  'store.nftWeed': {
    en: 'NFT Weed',
    id: 'NFT Ganja'
  },
  'store.automation': {
    en: 'Automation',
    id: 'Otomasi'
  },
  'store.miningHashrate': {
    en: 'Mining Hashrate +25%',
    id: 'Hashrate Mining +25%'
  },
  'store.increaseSpeed': {
    en: 'Increase WEED mining speed by 25%',
    id: 'Tingkatkan kecepatan mining WEED sebesar 25%'
  },
  'store.duration': {
    en: 'Duration: {days} days',
    id: 'Durasi: {days} hari'
  },
  'store.autoWatering': {
    en: 'Auto Watering System',
    id: 'Sistem Penyiraman Otomatis'
  },
  'store.autoWateringDesc': {
    en: 'Automatically waters plants every 6 hours',
    id: 'Otomatis menyiram tanaman setiap 6 jam'
  },
  'store.permanentUpgrade': {
    en: 'Permanent upgrade',
    id: 'Upgrade permanen'
  },
  'store.legendaryNFT': {
    en: 'Legendary NFT Weed Collection',
    id: 'Koleksi NFT Ganja Legendaris'
  },
  'store.goldenKush': {
    en: 'Golden Kush',
    id: 'Golden Kush'
  },
  'store.diamondOG': {
    en: 'Diamond OG',
    id: 'Diamond OG'
  },
  'store.fireHaze': {
    en: 'Fire Haze',
    id: 'Fire Haze'
  },
  'store.yieldBoost': {
    en: '+{percent}% yield',
    id: '+{percent}% hasil'
  },
  'store.randomNFT': {
    en: 'Random NFT Weed',
    id: 'NFT Ganja Acak'
  },
  'store.permanentHashrate': {
    en: 'Permanent hashrate boost',
    id: 'Boost hashrate permanen'
  },
  'store.megaHashrate': {
    en: 'Mega Hashrate x5',
    id: 'Mega Hashrate x5'
  },
  'store.fasterGeneration': {
    en: '5x faster WEED generation for 24 hours',
    id: '5x lebih cepat generasi WEED selama 24 jam'
  },
  'store.limitedTime': {
    en: 'Limited Time',
    id: 'Waktu Terbatas'
  },
  'store.farmExpansion': {
    en: 'Farm Expansion Slot',
    id: 'Slot Ekspansi Pertanian'
  },
  'store.addPlantSlot': {
    en: 'Add +1 plant slot to your farm',
    id: 'Tambah +1 slot tanaman ke pertanian Anda'
  },
  'store.growMultiple': {
    en: 'Grow multiple plants',
    id: 'Tanam beberapa tanaman'
  },
  'store.geneticsLab': {
    en: 'Genetics Lab Access',
    id: 'Akses Lab Genetika'
  },
  'store.customStrains': {
    en: 'Create custom weed strains',
    id: 'Buat strain ganja kustom'
  },
  'store.breedGenetics': {
    en: 'Breed rare genetics',
    id: 'Kembangbiakkan genetika langka'
  },
  'store.ethereumPayments': {
    en: 'Ethereum Layer 1 Payments',
    id: 'Pembayaran Ethereum Layer 1'
  },
  'store.premiumItems': {
    en: 'All premium items are purchased with ETH on Ethereum mainnet. Connect your wallet to proceed with purchases.',
    id: 'Semua item premium dibeli dengan ETH di mainnet Ethereum. Hubungkan dompet Anda untuk melanjutkan pembelian.'
  },
  'store.connectWallet': {
    en: 'Connect Ethereum Wallet',
    id: 'Hubungkan Dompet Ethereum'
  },
  'store.buy': {
    en: 'Buy',
    id: 'Beli'
  },
  'store.mint': {
    en: 'Mint',
    id: 'Mint'
  },
  'store.unlock': {
    en: 'Unlock',
    id: 'Buka'
  },

  // Leaderboard Page
  'leaderboard.title': {
    en: 'Leaderboard',
    id: 'Papan Peringkat'
  },
  'leaderboard.topFarmers': {
    en: 'Top WEED farmers this season',
    id: 'Petani WEED terbaik musim ini'
  },
  'leaderboard.weekly': {
    en: 'Weekly',
    id: 'Mingguan'
  },
  'leaderboard.monthly': {
    en: 'Monthly',
    id: 'Bulanan'
  },
  'leaderboard.allTime': {
    en: 'All Time',
    id: 'Sepanjang Masa'
  },
  'leaderboard.you': {
    en: '(You)',
    id: '(Anda)'
  },
  'leaderboard.earned': {
    en: '{amount} WEED earned',
    id: '{amount} WEED diperoleh'
  },
  'leaderboard.allRankings': {
    en: 'All Rankings',
    id: 'Semua Peringkat'
  },
  'leaderboard.positions': {
    en: '{count} positions',
    id: '{count} posisi'
  },
  'leaderboard.position': {
    en: '{count} position',
    id: '{count} posisi'
  },
  'leaderboard.same': {
    en: 'same',
    id: 'sama'
  },
  'leaderboard.viewMore': {
    en: 'View More Rankings',
    id: 'Lihat Peringkat Lainnya'
  },

  // Rewards Page
  'rewards.title': {
    en: 'Rewards',
    id: 'Hadiah'
  },
  'rewards.availableBalance': {
    en: 'Available Balance',
    id: 'Saldo Tersedia'
  },
  'rewards.withdrawalAvailable': {
    en: 'Withdrawal available at Level 10',
    id: 'Penarikan tersedia di Level 10'
  },
  'rewards.dailyCheckin': {
    en: 'Daily Check-in',
    id: 'Check-in Harian'
  },
  'rewards.day': {
    en: 'Day',
    id: 'Hari'
  },
  'rewards.claimToday': {
    en: "Claim Today's Reward",
    id: 'Klaim Hadiah Hari Ini'
  },
  'rewards.recentRewards': {
    en: 'Recent Rewards',
    id: 'Hadiah Terbaru'
  },
  'rewards.today': {
    en: 'Today',
    id: 'Hari ini'
  },
  'rewards.levelUpBonus': {
    en: 'Level Up Bonus',
    id: 'Bonus Naik Level'
  },
  'rewards.daysAgo': {
    en: '{count} days ago',
    id: '{count} hari yang lalu'
  },

  // Referral Page
  'referral.inviteFriends': {
    en: 'Invite Friends',
    id: 'Undang Teman'
  },
  'referral.earnTokens': {
    en: 'Earn WEED tokens for every friend who joins and harvests!',
    id: 'Dapatkan token WEED untuk setiap teman yang bergabung dan panen!'
  },
  'referral.totalInvited': {
    en: 'Total Invited',
    id: 'Total Diundang'
  },
  'referral.validReferrals': {
    en: 'Valid Referrals',
    id: 'Rujukan Valid'
  },
  'referral.weedEarned': {
    en: 'WEED Earned',
    id: 'WEED Diperoleh'
  },
  'referral.yourLink': {
    en: 'Your Referral Link',
    id: 'Link Rujukan Anda'
  },
  'referral.shareOnTelegram': {
    en: 'Share on Telegram',
    id: 'Bagikan di Telegram'
  },
  'referral.shareOnWhatsApp': {
    en: 'Share on WhatsApp',
    id: 'Bagikan di WhatsApp'
  },
  'referral.rewards': {
    en: 'Referral Rewards',
    id: 'Hadiah Rujukan'
  },
  'referral.friendJoins': {
    en: 'Friend Joins',
    id: 'Teman Bergabung'
  },
  'referral.whenSomeoneUses': {
    en: 'When someone uses your link',
    id: 'Ketika seseorang menggunakan link Anda'
  },
  'referral.validReferral': {
    en: 'Valid Referral',
    id: 'Rujukan Valid'
  },
  'referral.afterHarvests': {
    en: 'After 5 harvests completed',
    id: 'Setelah 5 panen selesai'
  },
  'referral.yourReferrals': {
    en: 'Your Referrals',
    id: 'Rujukan Anda'
  },
  'referral.validHarvests': {
    en: '({count} harvests)',
    id: '({count} panen)'
  },
  'referral.pendingHarvests': {
    en: '({count}/5 harvests)',
    id: '({count}/5 panen)'
  },
  'referral.inactive': {
    en: '({count} harvests)',
    id: '({count} panen)'
  },
  'referral.justJoined': {
    en: 'Just joined!',
    id: 'Baru bergabung!'
  },
  'referral.tips': {
    en: 'Referral Tips',
    id: 'Tips Rujukan'
  },
  'referral.shareOnSocial': {
    en: 'Share your link on social media for maximum reach',
    id: 'Bagikan link Anda di media sosial untuk jangkauan maksimal'
  },
  'referral.helpNewFarmers': {
    en: 'Help new farmers understand the game mechanics',
    id: 'Bantu petani baru memahami mekanisme permainan'
  },
  'referral.remindFriends': {
    en: 'Remind friends to water plants daily for faster growth',
    id: 'Ingatkan teman untuk menyiram tanaman setiap hari agar tumbuh lebih cepat'
  },
  'referral.validRequires': {
    en: 'Valid referrals require 5 completed harvests',
    id: 'Rujukan valid memerlukan 5 panen yang selesai'
  },

  // Profile Page
  'profile.weedEarned': {
    en: 'WEED Earned',
    id: 'WEED Diperoleh'
  },
  'profile.globalRank': {
    en: 'Global Rank',
    id: 'Peringkat Global'
  },
  'profile.plantsGrown': {
    en: 'Plants Grown',
    id: 'Tanaman Ditanam'
  },
  'profile.farmStats': {
    en: 'Farm Statistics',
    id: 'Statistik Pertanian'
  },
  'profile.totalHarvests': {
    en: 'Total Harvests',
    id: 'Total Panen'
  },
  'profile.daysActive': {
    en: 'Days Active',
    id: 'Hari Aktif'
  },
  'profile.bestStreak': {
    en: 'Best Streak',
    id: 'Streak Terbaik'
  },
  'profile.joined': {
    en: 'Joined',
    id: 'Bergabung'
  },
  'profile.achievements': {
    en: 'Achievements',
    id: 'Pencapaian'
  },
  'profile.firstPlant': {
    en: 'First Plant',
    id: 'Tanaman Pertama'
  },
  'profile.sevenDayStreak': {
    en: '7 Day Streak',
    id: 'Streak 7 Hari'
  },
  'profile.topTen': {
    en: 'Top 10',
    id: 'Top 10'
  },

  // Time units
  'time.hours': {
    en: '{count}h',
    id: '{count}j'
  },
  'time.minutes': {
    en: '{count}m',
    id: '{count}m'
  },
  'time.seconds': {
    en: '{count}s',
    id: '{count}d'
  },

  // Common
  'common.loading': {
    en: 'Loading...',
    id: 'Memuat...'
  },
  'common.error': {
    en: 'Error',
    id: 'Kesalahan'
  },
  'common.success': {
    en: 'Success',
    id: 'Berhasil'
  },
  'common.cancel': {
    en: 'Cancel',
    id: 'Batal'
  },
  'common.confirm': {
    en: 'Confirm',
    id: 'Konfirmasi'
  },
  'common.close': {
    en: 'Close',
    id: 'Tutup'
  }
}

export function useLang() {
  const [language, setLanguage] = useState<Language>('en')

  useEffect(() => {
    // Get language from localStorage or browser
    const savedLang = localStorage.getItem('language') as Language
    if (savedLang && (savedLang === 'en' || savedLang === 'id')) {
      setLanguage(savedLang)
    } else {
      // Detect browser language
      const browserLang = navigator.language.toLowerCase()
      if (browserLang.startsWith('id')) {
        setLanguage('id')
      }
    }
  }, [])

  const changeLanguage = (lang: Language) => {
    setLanguage(lang)
    localStorage.setItem('language', lang)
  }

  const t = (key: string, params?: Record<string, string | number>): string => {
    const translation = translations[key]
    if (!translation) {
      console.warn(`Translation missing for key: ${key}`)
      return key
    }

    let text = translation[language]
    
    // Replace parameters in the text
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        text = text.replace(new RegExp(`{${param}}`, 'g'), String(value))
      })
    }

    return text
  }

  return {
    language,
    changeLanguage,
    t,
    isEnglish: language === 'en',
    isIndonesian: language === 'id'
  }
}

