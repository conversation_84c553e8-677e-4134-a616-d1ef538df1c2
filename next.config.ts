import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  turbopack: {
    rules: {
      "*.svg": {
        loaders: ["@svgr/webpack"],
        as: "*.js",
      },
    },
  },
  images: {
    domains: ["i.imgur.com", "res.cloudinary.com"],
  },

  // redirects: async () => {
  //   return [
  //     {
  //       source: "/.well-known/farcaster.json",
  //       destination: "https://api.farcaster.xyz/miniapps/hosted-manifest/01979647-6df4-4a60-9bc5-dec059e26de9",
  //       permanent: false,
  //     },
  //   ]
  // }
};

export default nextConfig;
