.
├── LICENSE
├── README.md
├── components.json
├── directory-tree.txt
├── index.d.ts
├── next.config.ts
├── package-lock.json
├── package.json
├── postcss.config.mjs
├── prisma
│   ├── dev.db
│   ├── migrations
│   │   ├── 20250622063621_init
│   │   │   └── migration.sql
│   │   └── migration_lock.toml
│   └── schema.prisma
├── public
│   ├── avatars
│   │   └── weedfarmer.png
│   ├── icon.png
│   ├── icons
│   │   ├── cannabis-leaf.png
│   │   └── plant-stages.png
│   ├── images
│   │   └── main-plant.png
│   └── splash.png
├── scripts
│   ├── build.js
│   ├── deploy.js
│   └── dev.js
├── src
│   ├── app
│   │   ├── api
│   │   │   ├── auth
│   │   │   │   └── [...nextauth]
│   │   │   │       └── route.ts
│   │   │   ├── best-friends
│   │   │   │   └── route.ts
│   │   │   ├── leaderboard
│   │   │   │   └── route.ts
│   │   │   ├── opengraph-image
│   │   │   │   └── route.tsx
│   │   │   ├── plants
│   │   │   │   └── route.ts
│   │   │   ├── profile
│   │   │   │   └── route.ts
│   │   │   ├── referrals
│   │   │   │   └── route.ts
│   │   │   ├── rewards
│   │   │   │   └── route.ts
│   │   │   ├── send-notification
│   │   │   │   └── route.ts
│   │   │   ├── users
│   │   │   │   └── route.ts
│   │   │   └── webhook
│   │   │       └── route.ts
│   │   ├── app.tsx
│   │   ├── favicon.ico
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── providers.tsx
│   │   └── share
│   │       └── [fid]
│   │           └── page.tsx
│   ├── auth.ts
│   ├── components
│   │   ├── App.tsx
│   │   ├── Demo.tsx
│   │   ├── HomePage.tsx
│   │   ├── Layout.tsx
│   │   ├── LeaderboardPage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── RewardsPage.tsx
│   │   ├── StorePage.tsx
│   │   ├── providers
│   │   │   ├── SafeFarcasterSolanaProvider.tsx
│   │   │   └── WagmiProvider.tsx
│   │   └── ui
│   │       ├── Button.tsx
│   │       ├── Footer.tsx
│   │       ├── Header.tsx
│   │       ├── Share.tsx
│   │       ├── input.tsx
│   │       └── label.tsx
│   ├── data
│   │   ├── activities.ts
│   │   ├── leaderboard.ts
│   │   ├── referrals.ts
│   │   ├── rewards.ts
│   │   ├── seed.ts
│   │   ├── storeItems.ts
│   │   └── userProfile.ts
│   ├── hooks
│   │   └── useLang.ts
│   ├── lib
│   │   ├── constants.ts
│   │   ├── farcaster.ts
│   │   ├── kv.ts
│   │   ├── neynar.ts
│   │   ├── notifs.ts
│   │   ├── truncateAddress.ts
│   │   └── utils.ts
│   └── server
│       ├── db.ts
│       ├── plantService.ts
│       ├── referralService.ts
│       ├── rewardService.ts
│       └── userService.ts
├── tailwind.config.ts
├── test-server.cjs
├── tsconfig.json
└── vercel.json

32 directories, 82 files
