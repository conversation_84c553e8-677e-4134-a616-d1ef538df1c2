{"name": "weed-firm-app", "version": "0.1.0", "type": "module", "private": false, "access": "public", "exports": {".": {"types": "./index.d.ts", "import": "./bin/init.js"}}, "types": "./index.d.ts", "scripts": {"dev": "node scripts/dev.js", "next:dev": "next dev --turbo", "build": "node scripts/build.js", "start": "next start", "lint": "next lint", "deploy:vercel": "node scripts/deploy.js", "cleanup": "lsof -ti :3000 | xargs kill -9", "shadcn:add": "npx shadcn@latest add"}, "dependencies": {"@farcaster/auth-client": ">=0.3.0 <1.0.0", "@farcaster/auth-kit": ">=0.6.0 <1.0.0", "@farcaster/frame-core": ">=0.0.29 <1.0.0", "@farcaster/frame-node": ">=0.0.18 <1.0.0", "@farcaster/frame-sdk": ">=0.0.31 <1.0.0", "@farcaster/frame-wagmi-connector": ">=0.0.19 <1.0.0", "@farcaster/mini-app-solana": ">=0.0.17 <1.0.0", "@neynar/nodejs-sdk": "^2.46.0", "@neynar/react": "^1.2.2", "@prisma/client": "^6.10.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.7", "@solana/wallet-adapter-react": "^0.15.38", "@tanstack/react-query": "^5.61.0", "@upstash/redis": "^1.34.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "lucide-react": "^0.469.0", "mipd": "^0.0.7", "next": "^15", "next-auth": "^4.24.11", "prisma": "^6.10.1", "react": "^19", "react-dom": "^19", "sqlite3": "^5.1.7", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "viem": "^2.23.6", "wagmi": "^2.14.12", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "crypto": "^1.0.1", "eslint": "^8", "eslint-config-next": "15.0.3", "localtunnel": "^1.8.3", "pino-pretty": "^13.0.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5"}}